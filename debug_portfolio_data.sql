-- Debug queries to check portfolio data

-- Check if we have any assets
SELECT 'Total Assets' as check_name, COUNT(*) as count FROM assets;

-- Check if we have any portfolio value data
SELECT 'Portfolio Value Records' as check_name, COUNT(*) as count FROM portfolio_value_at_date;

-- Check timeframe data
SELECT 'Timeframe Data' as check_name, timeframe, COUNT(*) as count 
FROM portfolio_timeframe_data 
GROUP BY timeframe 
ORDER BY timeframe;

-- Sample portfolio values
SELECT 
  value_date,
  portfolio_value,
  active_assets
FROM portfolio_value_at_date
ORDER BY value_date
LIMIT 10;

-- Sample timeframe data for 1Y
SELECT 
  performance_date,
  total_value,
  display_label
FROM portfolio_timeframe_data
WHERE timeframe = '1Y'
ORDER BY performance_date
LIMIT 10;

-- Test the fixed chart axis labels

-- Test 1: Check portfolio_chart_clean for each timeframe
SELECT 
  'Clean Chart Data' as test_name,
  timeframe,
  COUNT(*) as record_count,
  STRING_AGG(display_label, ', ' ORDER BY sequence_number) as labels
FROM portfolio_chart_clean
GROUP BY timeframe
ORDER BY timeframe;

-- Test 2: Sample data for 6M timeframe
SELECT 
  '6M Sample' as test_name,
  performance_date,
  total_value,
  display_label,
  sequence_number
FROM portfolio_chart_clean
WHERE timeframe = '6M'
ORDER BY sequence_number
LIMIT 10;

-- Test 3: Sample data for 1Y timeframe
SELECT 
  '1Y Sample' as test_name,
  performance_date,
  total_value,
  display_label,
  sequence_number
FROM portfolio_chart_clean
WHERE timeframe = '1Y'
ORDER BY sequence_number
LIMIT 10;

-- Test 4: Sample data for 5Y timeframe
SELECT 
  '5Y Sample' as test_name,
  performance_date,
  total_value,
  display_label,
  sequence_number
FROM portfolio_chart_clean
WHERE timeframe = '5Y'
ORDER BY sequence_number
LIMIT 10;

-- Test 5: Check for duplicates in display labels
SELECT 
  'Duplicate Check' as test_name,
  timeframe,
  display_label,
  COUNT(*) as duplicate_count
FROM portfolio_chart_clean
GROUP BY timeframe, display_label
HAVING COUNT(*) > 1
ORDER BY timeframe, display_label;

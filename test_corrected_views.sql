-- Test the corrected portfolio performance views

-- Test 1: Check if we have portfolio evolution data
SELECT 
  'portfolio_value_evolution' as view_name,
  COUNT(*) as record_count,
  MIN(value_date) as earliest_date,
  MAX(value_date) as latest_date,
  MIN(portfolio_value) as min_value,
  MAX(portfolio_value) as max_value
FROM portfolio_value_evolution;

-- Test 2: Check monthly performance
SELECT 
  'monthly_portfolio_performance' as view_name,
  COUNT(*) as record_count,
  MIN(month) as earliest_month,
  MAX(month) as latest_month,
  MIN(avg_monthly_value) as min_value,
  MAX(avg_monthly_value) as max_value
FROM monthly_portfolio_performance;

-- Test 3: Check timeframe data
SELECT 
  'portfolio_timeframe_data' as view_name,
  timeframe,
  COUNT(*) as record_count,
  MIN(performance_date) as earliest_date,
  MAX(performance_date) as latest_date
FROM portfolio_timeframe_data
GROUP BY timeframe
ORDER BY timeframe;

-- Test 4: Sample portfolio evolution data
SELECT 
  value_date,
  portfolio_value,
  active_assets,
  daily_change_percentage
FROM portfolio_value_evolution
ORDER BY value_date
LIMIT 15;

-- Test 5: Sample timeframe data for 1Y
SELECT 
  performance_date,
  total_value,
  display_label
FROM portfolio_timeframe_data
WHERE timeframe = '1Y'
ORDER BY performance_date
LIMIT 10;

-- Test 6: Sample monthly data
SELECT 
  month,
  avg_monthly_value,
  monthly_change_percentage
FROM monthly_portfolio_performance
ORDER BY month
LIMIT 10;

/*
  # Improve portfolio performance tracking based on holding_date

  1. Changes
    - Create a better portfolio performance view that uses holding_date
    - Create a view for portfolio snapshots at specific dates
    - Add a view for time-series portfolio performance

  2. Performance Tracking Logic
    - Use holding_date as the primary date for performance calculations
    - For each date, get the latest asset values up to that date
    - Calculate portfolio value based on the most recent asset values
*/

-- Drop existing views to recreate them with better logic
DROP VIEW IF EXISTS monthly_portfolio_performance;
DROP VIEW IF EXISTS portfolio_performance_history;

-- Create a view that shows portfolio value at any given date
-- This gets the latest asset values up to a specific date
CREATE OR REPLACE VIEW portfolio_value_at_date AS
WITH asset_snapshots_with_dates AS (
  SELECT 
    user_id,
    asset_identifier,
    holding_date,
    quantity,
    unit_price,
    currency_symbol,
    snapshot_type,
    country,
    created_at,
    -- Get the latest snapshot for each asset up to each holding_date
    ROW_NUMBER() OVER (
      PARTITION BY user_id, asset_identifier, holding_date 
      ORDER BY created_at DESC
    ) as rn
  FROM assets
  WHERE holding_date IS NOT NULL
)
SELECT 
  user_id,
  holding_date as value_date,
  SUM(CASE WHEN snapshot_type != 'delete' THEN quantity * COALESCE(unit_price, 0) ELSE 0 END) as portfolio_value,
  COUNT(DISTINCT CASE WHEN snapshot_type != 'delete' THEN asset_identifier END) as active_assets,
  ARRAY_AGG(DISTINCT country) FILTER (WHERE country IS NOT NULL AND snapshot_type != 'delete') as countries
FROM asset_snapshots_with_dates
WHERE rn = 1
GROUP BY user_id, holding_date
ORDER BY user_id, holding_date;

-- Grant access to the view
GRANT SELECT ON portfolio_value_at_date TO authenticated;

-- Create a view for monthly portfolio performance based on holding_date
CREATE OR REPLACE VIEW monthly_portfolio_performance AS
WITH monthly_values AS (
  SELECT 
    user_id,
    DATE_TRUNC('month', value_date) as month,
    -- Get the latest portfolio value for each month
    LAST_VALUE(portfolio_value) OVER (
      PARTITION BY user_id, DATE_TRUNC('month', value_date)
      ORDER BY value_date
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) as month_end_value,
    MAX(value_date) OVER (
      PARTITION BY user_id, DATE_TRUNC('month', value_date)
    ) as last_date_in_month,
    ROW_NUMBER() OVER (
      PARTITION BY user_id, DATE_TRUNC('month', value_date)
      ORDER BY value_date DESC
    ) as rn
  FROM portfolio_value_at_date
)
SELECT 
  user_id,
  month,
  month_end_value as avg_monthly_value,
  month_end_value as max_monthly_value,
  month_end_value as min_monthly_value,
  last_date_in_month,
  LAG(month_end_value) OVER (PARTITION BY user_id ORDER BY month) as previous_month_value,
  CASE 
    WHEN LAG(month_end_value) OVER (PARTITION BY user_id ORDER BY month) > 0 THEN 
      ROUND(((month_end_value - LAG(month_end_value) OVER (PARTITION BY user_id ORDER BY month)) / 
             LAG(month_end_value) OVER (PARTITION BY user_id ORDER BY month) * 100)::numeric, 2)
    ELSE 0 
  END as monthly_change_percentage
FROM monthly_values
WHERE rn = 1
ORDER BY user_id, month;

-- Grant access to the view
GRANT SELECT ON monthly_portfolio_performance TO authenticated;

-- Create a view for daily portfolio performance history
CREATE OR REPLACE VIEW portfolio_performance_history AS
SELECT 
  user_id,
  value_date as performance_date,
  portfolio_value as total_value,
  active_assets as asset_count,
  LAG(portfolio_value) OVER (PARTITION BY user_id ORDER BY value_date) as previous_value,
  CASE 
    WHEN LAG(portfolio_value) OVER (PARTITION BY user_id ORDER BY value_date) > 0 THEN 
      ROUND(((portfolio_value - LAG(portfolio_value) OVER (PARTITION BY user_id ORDER BY value_date)) / 
             LAG(portfolio_value) OVER (PARTITION BY user_id ORDER BY value_date) * 100)::numeric, 2)
    ELSE 0 
  END as daily_change_percentage
FROM portfolio_value_at_date
ORDER BY user_id, value_date;

-- Grant access to the view
GRANT SELECT ON portfolio_performance_history TO authenticated;

-- Create a view for getting portfolio performance over different timeframes
CREATE OR REPLACE VIEW portfolio_performance_timeframes AS
WITH timeframe_data AS (
  SELECT 
    user_id,
    performance_date,
    total_value,
    CASE 
      WHEN performance_date >= CURRENT_DATE - INTERVAL '1 month' THEN '1M'
      WHEN performance_date >= CURRENT_DATE - INTERVAL '6 months' THEN '6M'
      WHEN performance_date >= CURRENT_DATE - INTERVAL '1 year' THEN '1Y'
      WHEN performance_date >= CURRENT_DATE - INTERVAL '5 years' THEN '5Y'
      ELSE 'ALL'
    END as timeframe
  FROM portfolio_performance_history
)
SELECT 
  user_id,
  timeframe,
  performance_date,
  total_value,
  CASE 
    WHEN timeframe = '1M' THEN 'Week ' || CEIL(EXTRACT(DAY FROM performance_date) / 7.0)::text
    WHEN timeframe IN ('6M', '1Y') THEN TO_CHAR(performance_date, 'Mon')
    WHEN timeframe = '5Y' THEN EXTRACT(YEAR FROM performance_date)::text
    ELSE TO_CHAR(performance_date, 'YYYY-MM-DD')
  END as display_label
FROM timeframe_data
WHERE timeframe != 'ALL'
ORDER BY user_id, timeframe, performance_date;

-- Grant access to the view
GRANT SELECT ON portfolio_performance_timeframes TO authenticated;

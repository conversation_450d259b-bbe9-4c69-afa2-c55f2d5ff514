/*
  # Add country support and portfolio performance tracking

  1. Changes
    - Add country column to assets table
    - Create portfolio_performance_history view for tracking portfolio value over time
    - Add indexes for performance queries

  2. Security
    - RLS policies remain unchanged as they are table-level
*/

-- Add country column to assets table
ALTER TABLE assets
  ADD COLUMN IF NOT EXISTS country text;

-- Set default country based on asset type and name for existing records
UPDATE assets 
SET country = CASE 
  WHEN name ILIKE '%apple%' OR name ILIKE '%tesla%' OR name ILIKE '%microsoft%' OR type ILIKE '%stock%' THEN 'USA'
  WHEN name ILIKE '%bitcoin%' OR name ILIKE '%ethereum%' OR type ILIKE '%crypto%' THEN 'Global'
  WHEN name ILIKE '%property%' OR name ILIKE '%real estate%' THEN 
    CASE 
      WHEN name ILIKE '%miami%' THEN 'USA'
      WHEN name ILIKE '%london%' THEN 'UK'
      WHEN name ILIKE '%tokyo%' THEN 'Japan'
      ELSE 'USA'
    END
  ELSE 'USA'
END
WHERE country IS NULL;

-- Drop and recreate the latest_assets view to include country
DROP VIEW IF EXISTS latest_assets;

CREATE VIEW latest_assets AS
SELECT DISTINCT ON (asset_identifier)
  id,
  asset_identifier,
  bucket_id,
  name,
  symbol,
  type,
  quantity,
  unit_price,
  currency_symbol,
  snapshot_type,
  holding_date,
  country,
  created_at,
  user_id
FROM assets
ORDER BY asset_identifier, created_at DESC;

-- Grant access to the view
GRANT SELECT ON latest_assets TO authenticated;

-- Create a view for portfolio performance history
-- This aggregates total portfolio value by date
CREATE OR REPLACE VIEW portfolio_performance_history AS
WITH daily_snapshots AS (
  SELECT 
    user_id,
    DATE(created_at) as performance_date,
    SUM(CASE WHEN snapshot_type != 'delete' THEN quantity * COALESCE(unit_price, 0) ELSE 0 END) as total_value,
    COUNT(DISTINCT CASE WHEN snapshot_type != 'delete' THEN asset_identifier END) as asset_count
  FROM assets
  WHERE snapshot_type IN ('create', 'update', 'delete')
  GROUP BY user_id, DATE(created_at)
),
cumulative_performance AS (
  SELECT 
    user_id,
    performance_date,
    total_value,
    asset_count,
    LAG(total_value) OVER (PARTITION BY user_id ORDER BY performance_date) as previous_value
  FROM daily_snapshots
)
SELECT 
  user_id,
  performance_date,
  total_value,
  asset_count,
  previous_value,
  CASE 
    WHEN previous_value > 0 THEN 
      ROUND(((total_value - previous_value) / previous_value * 100)::numeric, 2)
    ELSE 0 
  END as daily_change_percentage
FROM cumulative_performance
ORDER BY user_id, performance_date;

-- Grant access to the performance view
GRANT SELECT ON portfolio_performance_history TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assets_user_created_date
  ON assets (user_id, created_at);

CREATE INDEX IF NOT EXISTS idx_assets_country
  ON assets (country);

-- Create a view for monthly portfolio performance (for charts)
CREATE OR REPLACE VIEW monthly_portfolio_performance AS
WITH monthly_data AS (
  SELECT 
    user_id,
    DATE_TRUNC('month', performance_date) as month,
    AVG(total_value) as avg_monthly_value,
    MAX(total_value) as max_monthly_value,
    MIN(total_value) as min_monthly_value,
    MAX(performance_date) as last_date_in_month
  FROM portfolio_performance_history
  GROUP BY user_id, DATE_TRUNC('month', performance_date)
)
SELECT 
  user_id,
  month,
  avg_monthly_value,
  max_monthly_value,
  min_monthly_value,
  last_date_in_month,
  LAG(avg_monthly_value) OVER (PARTITION BY user_id ORDER BY month) as previous_month_value,
  CASE 
    WHEN LAG(avg_monthly_value) OVER (PARTITION BY user_id ORDER BY month) > 0 THEN 
      ROUND(((avg_monthly_value - LAG(avg_monthly_value) OVER (PARTITION BY user_id ORDER BY month)) / 
             LAG(avg_monthly_value) OVER (PARTITION BY user_id ORDER BY month) * 100)::numeric, 2)
    ELSE 0 
  END as monthly_change_percentage
FROM monthly_data
ORDER BY user_id, month;

-- Grant access to the monthly performance view
GRANT SELECT ON monthly_portfolio_performance TO authenticated;

-- Test queries to verify the new performance tracking views work correctly

-- Test 1: Check portfolio_value_at_date view
SELECT 
  'portfolio_value_at_date' as view_name,
  COUNT(*) as record_count,
  MIN(value_date) as earliest_date,
  MAX(value_date) as latest_date,
  MIN(portfolio_value) as min_value,
  MAX(portfolio_value) as max_value
FROM portfolio_value_at_date;

-- Test 2: Check monthly_portfolio_performance view
SELECT 
  'monthly_portfolio_performance' as view_name,
  COUNT(*) as record_count,
  MIN(month) as earliest_month,
  MAX(month) as latest_month,
  MIN(avg_monthly_value) as min_value,
  MAX(avg_monthly_value) as max_value
FROM monthly_portfolio_performance;

-- Test 3: Check portfolio_performance_timeframes view
SELECT 
  'portfolio_performance_timeframes' as view_name,
  timeframe,
  COUNT(*) as record_count,
  MIN(performance_date) as earliest_date,
  MAX(performance_date) as latest_date
FROM portfolio_performance_timeframes
GROUP BY timeframe
ORDER BY timeframe;

-- Test 4: Sample data from portfolio_value_at_date
SELECT 
  value_date,
  portfolio_value,
  active_assets,
  countries
FROM portfolio_value_at_date
ORDER BY value_date
LIMIT 10;

-- Test 5: Sample data from monthly_portfolio_performance
SELECT 
  month,
  avg_monthly_value,
  monthly_change_percentage,
  last_date_in_month
FROM monthly_portfolio_performance
ORDER BY month
LIMIT 10;

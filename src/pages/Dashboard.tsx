import React, { useState, useEffect } from 'react';
import { Plus, TrendingUp, TrendingDown, DollarSign, FileText, RefreshCw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { AssetSummary } from '../components/AssetSummary';
import { PriceChart } from '../components/PriceChart';
import { AllAssets } from '../components/AllAssets';
import { CountryDistribution } from '../components/CountryDistribution';
import { SnapshotTest } from '../components/SnapshotTest';
import { supabase } from '../utils/supabaseClient';

export const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [currency, setCurrency] = useState<'USD' | 'EUR' | 'GBP'>('USD');
  const [assets, setAssets] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  const currencySymbols = {
    USD: '$',
    EUR: '€',
    GBP: '£'
  };

  useEffect(() => {
    fetchAssets();
  }, []);

  const fetchAssets = async () => {
    try {
      const { data, error } = await supabase
        .from('latest_assets')
        .select('*')
        .neq('snapshot_type', 'delete')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAssets(data || []);
    } catch (error) {
      console.error('Error fetching assets:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleCurrency = () => {
    const currencies: ('USD' | 'EUR' | 'GBP')[] = ['USD', 'EUR', 'GBP'];
    const currentIndex = currencies.indexOf(currency);
    const nextIndex = (currentIndex + 1) % currencies.length;
    setCurrency(currencies[nextIndex]);
  };

  const calculateXIRR = () => {
    // Simplified XIRR calculation - in real app, use proper XIRR algorithm
    if (assets.length === 0) return 0;
    const totalValue = assets.reduce((sum, asset) => sum + (asset.quantity * asset.unit_price), 0);
    const totalCost = assets.reduce((sum, asset) => sum + (asset.quantity * asset.unit_price * 0.9), 0); // Assuming 10% gain
    return totalCost > 0 ? ((totalValue - totalCost) / totalCost * 100) : 0;
  };

  const handleAddAsset = () => {
    navigate('/upload');
  };

  return (
    <div className="bg-white min-h-screen">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-black">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-600">
            Track your assets and their performance
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 md:mt-0">
          <button
            onClick={toggleCurrency}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-black bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-150"
          >
            <RefreshCw className="-ml-1 mr-2 h-4 w-4" />
            {currency}
          </button>
          <button
            onClick={handleAddAsset}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-150"
          >
            <Plus className="-ml-1 mr-2 h-5 w-5" />
            Add Asset
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
        <div className="bg-white border border-gray-200 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-black rounded-md p-3">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-600 truncate">Total Value</dt>
                  <dd>
                    <div className="text-lg font-medium text-black">
                      {currencySymbols[currency]}{assets.reduce((sum, asset) => sum + (asset.quantity * asset.unit_price), 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="font-medium text-black inline-flex items-center">
                <TrendingUp className="mr-1 h-4 w-4" />
                4.3%
              </span>
              <span className="text-gray-500 ml-2">from last month</span>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-black rounded-md p-3">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-600 truncate">Profitable Assets</dt>
                  <dd>
                    <div className="text-lg font-medium text-black">
                      {assets.filter(asset => asset.unit_price > (asset.unit_price * 0.9)).length}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="font-medium text-black inline-flex items-center">
                <TrendingUp className="mr-1 h-4 w-4" />
                12.5%
              </span>
              <span className="text-gray-500 ml-2">increase</span>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-gray-600 rounded-md p-3">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-600 truncate">Declining Assets</dt>
                  <dd>
                    <div className="text-lg font-medium text-black">
                      {assets.filter(asset => asset.unit_price < (asset.unit_price * 0.9)).length}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="font-medium text-gray-600 inline-flex items-center">
                <TrendingDown className="mr-1 h-4 w-4" />
                2.7%
              </span>
              <span className="text-gray-500 ml-2">decrease</span>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-gray-800 rounded-md p-3">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-600 truncate">Total Assets</dt>
                  <dd>
                    <div className="text-lg font-medium text-black">{assets.length}</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <a href="/portfolio" className="font-medium text-black hover:text-gray-600">
                View all
              </a>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-black rounded-md p-3">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-600 truncate">XIRR</dt>
                  <dd>
                    <div className="text-lg font-medium text-black">{calculateXIRR().toFixed(2)}%</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="text-gray-500">Annualized return</span>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 bg-white border border-gray-200 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-black mb-4">Portfolio Performance</h2>
          <PriceChart />
        </div>
        <div className="bg-white border border-gray-200 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-black mb-4">Asset Distribution</h2>
          <AssetSummary assets={assets} currency={currency} />
        </div>
      </div>

      <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="bg-white border border-gray-200 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-black mb-4">Country Distribution</h2>
          <CountryDistribution assets={assets} />
        </div>
        <div className="bg-white border border-gray-200 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-black mb-4">All Assets</h2>
          <AllAssets assets={assets} currency={currency} loading={loading} />
        </div>
      </div>

      {/* Snapshot Pattern Test Component */}
      <div className="mt-8">
        <SnapshotTest />
      </div>
    </div>
  );
};
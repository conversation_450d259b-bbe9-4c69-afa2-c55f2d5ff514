import React, { useState } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';

export const PriceChart: React.FC = () => {
  const [activeTimeframe, setActiveTimeframe] = useState<string>('1Y');

  const timeframes = ['1M', '6M', '1Y', '5Y'];

  // Sample data for different timeframes
  const dataMap = {
    '1M': [
      { name: 'Week 1', value: 24000 },
      { name: 'Week 2', value: 24500 },
      { name: 'Week 3', value: 23800 },
      { name: 'Week 4', value: 25200 }
    ],
    '6M': [
      { name: 'Jan', value: 20000 },
      { name: 'Feb', value: 21000 },
      { name: 'Mar', value: 22500 },
      { name: 'Apr', value: 23000 },
      { name: 'May', value: 24000 },
      { name: 'Jun', value: 25200 }
    ],
    '1Y': [
      { name: '<PERSON>', value: 18000 },
      { name: 'Feb', value: 19000 },
      { name: 'Mar', value: 20500 },
      { name: 'Apr', value: 19780 },
      { name: 'May', value: 21890 },
      { name: 'Jun', value: 22390 },
      { name: 'Jul', value: 23490 },
      { name: 'Aug', value: 24000 },
      { name: 'Sep', value: 24500 },
      { name: 'Oct', value: 25200 },
      { name: 'Nov', value: 24800 },
      { name: 'Dec', value: 26000 }
    ],
    '5Y': [
      { name: '2020', value: 15000 },
      { name: '2021', value: 18000 },
      { name: '2022', value: 16500 },
      { name: '2023', value: 22000 },
      { name: '2024', value: 26000 }
    ]
  };

  const data = dataMap[activeTimeframe as keyof typeof dataMap];

  return (
    <div>
      <div className="mb-4 flex space-x-2 overflow-x-auto">
        {timeframes.map((timeframe) => (
          <button
            key={timeframe}
            className={`px-3 py-1 text-sm rounded-md ${
              activeTimeframe === timeframe
                ? 'bg-black text-white font-medium'
                : 'text-gray-500 hover:bg-gray-100 border border-gray-300'
            } transition-colors duration-150`}
            onClick={() => setActiveTimeframe(timeframe)}
          >
            {timeframe}
          </button>
        ))}
      </div>

      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
            <XAxis dataKey="name" stroke="#666" />
            <YAxis stroke="#666" />
            <Tooltip
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #ccc',
                borderRadius: '4px',
                color: 'black'
              }}
            />
            <Line
              type="monotone"
              dataKey="value"
              stroke="#000000"
              activeDot={{ r: 8, fill: '#000000' }}
              strokeWidth={2}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};
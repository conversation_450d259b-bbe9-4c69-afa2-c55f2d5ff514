import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { supabase } from '../utils/supabaseClient';

interface PerformanceData {
  name: string;
  value: number;
  date: string;
}

export const PriceChart: React.FC = () => {
  const [activeTimeframe, setActiveTimeframe] = useState<string>('1Y');
  const [data, setData] = useState<PerformanceData[]>([]);
  const [loading, setLoading] = useState(true);

  const timeframes = ['1M', '6M', '1Y', '5Y'];

  useEffect(() => {
    fetchPerformanceData();
  }, [activeTimeframe]);

  const fetchPerformanceData = async () => {
    setLoading(true);
    try {
      // Try to get data from the corrected timeframe-specific view
      const { data: timeframeData, error: timeframeError } = await supabase
        .from('portfolio_timeframe_data')
        .select('*')
        .eq('timeframe', activeTimeframe)
        .order('performance_date', { ascending: true });

      if (timeframeError) {
        console.log('Timeframe view error:', timeframeError);
      }

      if (timeframeData && timeframeData.length > 0) {
        // Use the pre-formatted data from the view
        const chartData = timeframeData.map((item: any) => ({
          name: item.display_label,
          value: Math.round(item.total_value || 0),
          date: item.performance_date
        }));

        setData(chartData);
      } else {
        // Fallback to portfolio_value_evolution with manual filtering
        const { data: portfolioData, error: portfolioError } = await supabase
          .from('portfolio_value_evolution')
          .select('*')
          .order('value_date', { ascending: true });

        if (portfolioError) throw portfolioError;

        if (portfolioData && portfolioData.length > 0) {
          // Filter and format data based on timeframe
          const now = new Date();
          let startDate = new Date();

          switch (activeTimeframe) {
            case '1M':
              startDate.setMonth(now.getMonth() - 1);
              break;
            case '6M':
              startDate.setMonth(now.getMonth() - 6);
              break;
            case '1Y':
              startDate.setFullYear(now.getFullYear() - 1);
              break;
            case '5Y':
              startDate.setFullYear(now.getFullYear() - 5);
              break;
          }

          const filteredData = portfolioData.filter(item =>
            new Date(item.value_date) >= startDate
          );

          if (filteredData.length > 0) {
            const chartData = filteredData.map((item: any) => ({
              name: formatDateLabel(item.value_date, activeTimeframe),
              value: Math.round(item.portfolio_value || 0),
              date: item.value_date
            }));

            setData(chartData);
          } else {
            // Fallback to sample data if no real data available
            setData(getFallbackData());
          }
        } else {
          // Fallback to sample data if no real data available
          setData(getFallbackData());
        }
      }
    } catch (error) {
      console.error('Error fetching performance data:', error);
      // Fallback to sample data if no real data available
      setData(getFallbackData());
    } finally {
      setLoading(false);
    }
  };

  const formatDateLabel = (dateString: string, timeframe: string): string => {
    const date = new Date(dateString);

    switch (timeframe) {
      case '1M':
        return `Week ${Math.ceil(date.getDate() / 7)}`;
      case '6M':
      case '1Y':
        return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
      case '5Y':
        return date.getFullYear().toString();
      default:
        return date.toLocaleDateString('en-US', { month: 'short' });
    }
  };

  const getFallbackData = (): PerformanceData[] => {
    // Fallback data when no real data is available
    const fallbackDataMap = {
      '1M': [
        { name: 'Week 1', value: 24000, date: '2024-01-01' },
        { name: 'Week 2', value: 24500, date: '2024-01-08' },
        { name: 'Week 3', value: 23800, date: '2024-01-15' },
        { name: 'Week 4', value: 25200, date: '2024-01-22' }
      ],
      '6M': [
        { name: 'Jan', value: 20000, date: '2024-01-01' },
        { name: 'Feb', value: 21000, date: '2024-02-01' },
        { name: 'Mar', value: 22500, date: '2024-03-01' },
        { name: 'Apr', value: 23000, date: '2024-04-01' },
        { name: 'May', value: 24000, date: '2024-05-01' },
        { name: 'Jun', value: 25200, date: '2024-06-01' }
      ],
      '1Y': [
        { name: 'Jan', value: 18000, date: '2024-01-01' },
        { name: 'Feb', value: 19000, date: '2024-02-01' },
        { name: 'Mar', value: 20500, date: '2024-03-01' },
        { name: 'Apr', value: 19780, date: '2024-04-01' },
        { name: 'May', value: 21890, date: '2024-05-01' },
        { name: 'Jun', value: 22390, date: '2024-06-01' },
        { name: 'Jul', value: 23490, date: '2024-07-01' },
        { name: 'Aug', value: 24000, date: '2024-08-01' },
        { name: 'Sep', value: 24500, date: '2024-09-01' },
        { name: 'Oct', value: 25200, date: '2024-10-01' },
        { name: 'Nov', value: 24800, date: '2024-11-01' },
        { name: 'Dec', value: 26000, date: '2024-12-01' }
      ],
      '5Y': [
        { name: '2020', value: 15000, date: '2020-01-01' },
        { name: '2021', value: 18000, date: '2021-01-01' },
        { name: '2022', value: 16500, date: '2022-01-01' },
        { name: '2023', value: 22000, date: '2023-01-01' },
        { name: '2024', value: 26000, date: '2024-01-01' }
      ]
    };

    return fallbackDataMap[activeTimeframe as keyof typeof fallbackDataMap] || fallbackDataMap['1Y'];
  };

  return (
    <div>
      <div className="mb-4 flex space-x-2 overflow-x-auto">
        {timeframes.map((timeframe) => (
          <button
            key={timeframe}
            className={`px-3 py-1 text-sm rounded-md ${
              activeTimeframe === timeframe
                ? 'bg-black text-white font-medium'
                : 'text-gray-500 hover:bg-gray-100 border border-gray-300'
            } transition-colors duration-150`}
            onClick={() => setActiveTimeframe(timeframe)}
          >
            {timeframe}
          </button>
        ))}
      </div>

      <div className="h-80">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
              <XAxis dataKey="name" stroke="#666" />
              <YAxis
                stroke="#666"
                tickFormatter={(value) => `$${value.toLocaleString()}`}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  color: 'black'
                }}
                formatter={(value: number) => [`$${value.toLocaleString()}`, 'Portfolio Value']}
                labelFormatter={(label) => `Period: ${label}`}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#000000"
                activeDot={{ r: 8, fill: '#000000' }}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        )}
      </div>
    </div>
  );
};
import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from 'recharts';

interface Asset {
  id: string;
  name: string;
  symbol: string;
  type: string;
  quantity: number;
  unit_price: number;
  holding_date?: string | null;
}

interface AssetSummaryProps {
  assets: Asset[];
  currency: 'USD' | 'EUR' | 'GBP';
}

export const AssetSummary: React.FC<AssetSummaryProps> = ({ assets, currency }) => {
  const [selectedCountry, setSelectedCountry] = useState<string>('All');

  const currencySymbols = {
    USD: '$',
    EUR: '€',
    GBP: '£'
  };

  // Mock function to get country from asset
  const getCountryFromAsset = (asset: Asset): string => {
    if (asset.name.toLowerCase().includes('apple') || asset.name.toLowerCase().includes('tesla')) return 'USA';
    if (asset.name.toLowerCase().includes('bitcoin') || asset.name.toLowerCase().includes('ethereum')) return 'Global';
    if (asset.name.toLowerCase().includes('property') || asset.name.toLowerCase().includes('real estate')) {
      if (asset.name.toLowerCase().includes('miami')) return 'USA';
      if (asset.name.toLowerCase().includes('london')) return 'UK';
      return 'USA';
    }
    if (asset.type.toLowerCase().includes('stock')) return 'USA';
    if (asset.type.toLowerCase().includes('crypto')) return 'Global';
    return 'USA';
  };

  // Filter assets by country
  const filteredAssets = selectedCountry === 'All'
    ? assets
    : assets.filter(asset => getCountryFromAsset(asset) === selectedCountry);

  // Get unique countries
  const countries = ['All', ...Array.from(new Set(assets.map(getCountryFromAsset)))];

  // Group assets by type
  const assetTypeData = filteredAssets.reduce((acc, asset) => {
    const type = asset.type || 'Other';
    const value = asset.quantity * asset.unit_price;

    if (acc[type]) {
      acc[type] += value;
    } else {
      acc[type] = value;
    }

    return acc;
  }, {} as Record<string, number>);

  const chartData = Object.entries(assetTypeData).map(([type, value]) => ({
    name: type,
    value: value,
    percentage: ((value / Object.values(assetTypeData).reduce((sum, val) => sum + val, 0)) * 100).toFixed(1)
  }));

  const COLORS = ['#000000', '#404040', '#808080', '#C0C0C0', '#E0E0E0'];

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded shadow-lg">
          <p className="text-black font-medium">{data.name}</p>
          <p className="text-gray-600">
            Value: {currencySymbols[currency]}{data.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </p>
          <p className="text-gray-600">Percentage: {data.percentage}%</p>
        </div>
      );
    }
    return null;
  };

  if (assets.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No assets to display.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Country:</label>
        <select
          value={selectedCountry}
          onChange={(e) => setSelectedCountry(e.target.value)}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-black focus:border-black text-sm"
        >
          {countries.map(country => (
            <option key={country} value={country}>{country}</option>
          ))}
        </select>
      </div>

      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percentage }) => `${name} ${percentage}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};
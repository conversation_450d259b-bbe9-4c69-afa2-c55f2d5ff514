import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from 'recharts';

interface Asset {
  id: string;
  name: string;
  symbol: string;
  type: string;
  quantity: number;
  unit_price: number;
  holding_date?: string | null;
  country?: string;
}

interface CountryDistributionProps {
  assets: Asset[];
}

export const CountryDistribution: React.FC<CountryDistributionProps> = ({ assets }) => {
  // Use real country data from database, with fallback logic for assets without country
  const getCountryFromAsset = (asset: Asset): string => {
    // Use the country field from database if available
    if (asset.country) {
      return asset.country;
    }

    // Fallback logic for assets without country data
    if (asset.name.toLowerCase().includes('apple') || asset.name.toLowerCase().includes('tesla') || asset.name.toLowerCase().includes('microsoft')) return 'USA';
    if (asset.name.toLowerCase().includes('bitcoin') || asset.name.toLowerCase().includes('ethereum')) return 'Global';
    if (asset.name.toLowerCase().includes('property') || asset.name.toLowerCase().includes('real estate')) {
      if (asset.name.toLowerCase().includes('miami')) return 'USA';
      if (asset.name.toLowerCase().includes('london')) return 'UK';
      if (asset.name.toLowerCase().includes('tokyo')) return 'Japan';
      return 'USA';
    }
    if (asset.type.toLowerCase().includes('stock')) return 'USA';
    if (asset.type.toLowerCase().includes('crypto')) return 'Global';
    return 'USA';
  };

  const countryData = assets.reduce((acc, asset) => {
    const country = getCountryFromAsset(asset);
    const value = asset.quantity * asset.unit_price;
    
    if (acc[country]) {
      acc[country] += value;
    } else {
      acc[country] = value;
    }
    
    return acc;
  }, {} as Record<string, number>);

  const chartData = Object.entries(countryData).map(([country, value]) => ({
    name: country,
    value: value,
    percentage: ((value / Object.values(countryData).reduce((sum, val) => sum + val, 0)) * 100).toFixed(1)
  }));

  const COLORS = ['#000000', '#404040', '#808080', '#C0C0C0', '#E0E0E0'];

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded shadow-lg">
          <p className="text-black font-medium">{data.name}</p>
          <p className="text-gray-600">
            Value: ${data.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </p>
          <p className="text-gray-600">Percentage: {data.percentage}%</p>
        </div>
      );
    }
    return null;
  };

  if (assets.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No assets to display country distribution.</p>
      </div>
    );
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percentage }) => `${name} ${percentage}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

-- Test the 1M chart fix

-- Test 1: Check what data we have in portfolio_value_evolution
SELECT 
  'Portfolio Evolution Data' as test_name,
  COUNT(*) as total_records,
  MIN(value_date) as earliest_date,
  MAX(value_date) as latest_date
FROM portfolio_value_evolution;

-- Test 2: Check 1M timeframe data from portfolio_chart_clean
SELECT 
  '1M Chart Data' as test_name,
  performance_date,
  total_value,
  display_label,
  sequence_number
FROM portfolio_chart_clean
WHERE timeframe = '1M'
ORDER BY performance_date;

-- Test 3: Check if we have recent data (last 30 days from latest date)
WITH latest_date AS (
  SELECT MAX(value_date) as max_date FROM portfolio_value_evolution
)
SELECT 
  'Recent Data Check' as test_name,
  value_date,
  portfolio_value,
  (SELECT max_date FROM latest_date) - value_date as days_from_latest
FROM portfolio_value_evolution
CROSS JOIN latest_date
WHERE value_date >= (SELECT max_date FROM latest_date) - INTERVAL '30 days'
ORDER BY value_date;

-- Test 4: Check all timeframes for proper ordering
SELECT 
  'All Timeframes' as test_name,
  timeframe,
  COUNT(*) as record_count,
  MIN(performance_date) as earliest_date,
  MAX(performance_date) as latest_date
FROM portfolio_chart_clean
GROUP BY timeframe
ORDER BY timeframe;
